{"name": "virtual-venus", "version": "2.2.1", "type": "module", "license": "MIT", "scripts": {"astro": "astro", "sync": "astro sync", "dev": "astro dev", "check": "astro check", "build": "astro build", "preview": "astro preview", "format": "prettier . --check --ignore-unknown", "format:write": "prettier . --write --ignore-unknown --cache", "lint": "eslint . --report-unused-disable-directives --max-warnings 0 --cache", "lint:fix": "eslint . --fix --cache", "toolbar:on": "astro preferences enable devToolbar", "toolbar:off": "astro preferences disable dev<PERSON><PERSON><PERSON>", "prepare": "simple-git-hooks", "postbuild": "pagefind --site dist && del-cli \"dist/pagefind/*ui*\""}, "devDependencies": {"@ascorbic/feed-loader": "^1.0.4", "@astrojs/check": "^0.9.4", "@astrojs/markdown-remark": "^6.3.3", "@astrojs/mdx": "^4.3.1", "@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.4.1", "@atproto/api": "^0.15.26", "@eslint/js": "^9.31.0", "@expressive-code/plugin-collapsible-sections": "^0.41.3", "@expressive-code/plugin-line-numbers": "^0.41.3", "@iconify/json": "^2.2.360", "@types/node": "^24.0.15", "@types/nprogress": "^0.2.3", "@types/p5": "^1.7.6", "@typescript-eslint/parser": "^8.37.0", "@unocss/reset": "66.3.3", "astro": "^5.12.1", "astro-expressive-code": "^0.41.3", "astro-loader-bluesky-posts": "^1.2.2", "astro-loader-github-prs": "^1.2.1", "astro-loader-github-releases": "^2.0.2", "astro-robots-txt": "^1.0.0", "chalk": "^5.4.1", "del-cli": "^6.0.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "globals": "^16.3.0", "html-entities": "^2.6.0", "lint-staged": "^15.5.1", "mdast-util-to-string": "^4.0.0", "nprogress": "^0.2.0", "p5": "^1.11.3", "pagefind": "^1.3.0", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "reading-time": "^1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-callouts": "^2.1.2", "rehype-external-links": "^3.0.0", "rehype-katex": "^7.0.1", "remark-directive": "^4.0.0", "remark-directive-sugar": "^1.1.2", "remark-imgattr": "^1.0.5", "remark-math": "^6.0.0", "satori": "^0.15.2", "satori-html": "^0.3.2", "sharp": "^0.34.3", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "unist-util-visit": "^5.0.0", "unocss": "66.3.3", "viewerjs": "^1.11.7"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": ["eslint"]}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "sharp", "simple-git-hooks"]}, "engines": {"node": "18.20.8 || ^20.9.0 || ^22.0.0"}}