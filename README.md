# Astro AntfuStyle Theme

[![ci][ci-badge]][ci-link]
[![version][version-badge]][version-link]
[![live demo][demo-badge]][demo-link]
[![chat][chat-badge]][chat-link]

![cover image](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/cover_2x.png)

AntfuStyle is a flexible and feature-rich [Astro 5](https://astro.build/) theme for developers and creators, inspired by the minimalist style of [antfu.me](https://antfu.me/). I like this design and have recreated and expanded its functionality.

## Features

**Optimized Infrastructure**

- Zero UI Framework
- SEO-Friendly
- Auto-Generated RSS Feed
- Dynamic OG Image Generation
- Subpath Deployment Support
- Optimized and Responsive Images
- VS Code Snippets for Fast Coding
- Well-Structured Project
- High Lighthouse Performance

**Content Management**

- Markdown & MDX Support
- Math Equations Rendering
- Callouts (Alerts/Admonitions)
- Code Syntax Highlighting & Annotations
- Ready-To-Use Remark Directives
- Customizable Layouts, Views, and Navigation
- Configurable Animated Backgrounds ([Preview](#preview))
- Showcase Your GitHub Releases and PRs
- Highlight Your Creative Work or Curated Posts
- Share Your Short Notes or Quick Thoughts
- Create Your Personal Gallery

**User Experience**

- Responsive Layout
- Full-Platform TOC Support
- Light & Dark Modes Toggle
- Smooth View Transitions
- Loading Progress Indicator
- Search Functionality
- Image Zoom Preview
- Social Media Sharing
- Integrated Giscus Comments
- Accessibility Enhancements
- Keyboard-Friendly

> [More features](https://github.com/users/lin-stephanie/projects/4) are continuously being added! 🚀

## Docs

The [live demo][demo-link] serves as self-documentation, detailing how to configure and use the theme, along with other relevant knowledge expansion. Explore [posts](https://astro-antfustyle-theme.vercel.app/blog/) on the demo site or view [Markdown files](https://github.com/lin-stephanie/astro-antfustyle-theme/tree/main/src/content/blog) in the repository. Quick Navigation:

- [Getting Started](https://astro-antfustyle-theme.vercel.app/blog/getting-started/)
- [Project Structure](https://astro-antfustyle-theme.vercel.app/blog/project-structure/)
- [Configuration](https://astro-antfustyle-theme.vercel.app/blog/basic-configuration/)
- [Authoring Content](https://astro-antfustyle-theme.vercel.app/blog/getting-started/#authoring-content)
- [Sync Updates](https://astro-antfustyle-theme.vercel.app/blog/sync-updates/)
- [Explore More](https://astro-antfustyle-theme.vercel.app/blog/getting-started/#next-steps)

If you're eager to deploy your own version right away:

[![Deploy with Netlify](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/deploy_netlify.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/lin-stephanie/astro-antfustyle-theme) [![Deploy with Vercel](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/deploy_vercel.svg)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Flin-stephanie%2Fastro-antfustyle-theme&project-name=astro-antfustyle-theme)

## Preview

![plum](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/blog_2x.png)

![rose](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/post_2x.png)

![dot](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/projects_2x.png)

![particle](https://raw.githubusercontent.com/lin-stephanie/assets/refs/heads/main/astro-antfustyle-theme/streams_2x.png)

## Credits

Thanks to the following projects for inspiration and references:

- [antfu/antfu.me](https://github.com/antfu/antfu.me)
- [satnaing/astro-paper](https://github.com/satnaing/astro-paper)
- [chrismwilliams/astro-theme-cactus](https://github.com/chrismwilliams/astro-theme-cactus)
- [saicaca/fuwari](https://github.com/saicaca/fuwari)

## Contribution

If you see any errors or room for improvement, feel free to open an [issue](https://github.com/lin-stephanie/astro-antfustyle-theme/issues) or [pull request](https://github.com/lin-stephanie/astro-antfustyle-theme/pulls). Thank you in advance for contributing! ❤️

## License

[MIT](https://github.com/lin-stephanie/astro-antfustyle-theme/blob/main/LICENSE) © 2024-PRESENT [Stephanie Lin](https://github.com/lin-stephanie)

<!-- Badges -->

[ci-badge]: https://img.shields.io/github/actions/workflow/status/lin-stephanie/astro-antfustyle-theme/ci.yml?label=CI&style=flat&colorA=080f12&colorB=f87171
[ci-link]: https://github.com/lin-stephanie/astro-antfustyle-theme/actions/workflows/ci.yml

[version-badge]: https://img.shields.io/github/v/release/lin-stephanie/astro-antfustyle-theme?label=Release&style=flat&colorA=080f12&colorB=f87171
[version-link]: https://github.com/lin-stephanie/astro-antfustyle-theme/releases

[demo-badge]: https://img.shields.io/badge/Live%20Demo-080f12?style=flat&colorA=080f12&colorB=f87171&logo=vercel&logoSize=10
[demo-link]:https://astro-antfustyle-theme.vercel.app/

[chat-badge]: https://img.shields.io/badge/Discussions-080f12?style=flat&colorA=080f12&colorB=f87171&logo=github
[chat-link]: https://github.com/lin-stephanie/astro-antfustyle-theme/discussions
