name: Release

run-name: Release ${{ github.ref_name }}

on:
  push:
    tags:
      - '*'

permissions:
  contents: write

jobs:
  release:
    if: github.repository == 'lin-stephanie/astro-antfustyle-theme'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Release
        run: npx changelogithub
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
