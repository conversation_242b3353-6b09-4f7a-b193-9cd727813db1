name: CI

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened

permissions:
  contents: read

jobs:
  ci:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        node: [lts/*]
      fail-fast: false
    env:
      GITHUB_TOKEN: ${{ secrets.GH_TOKEN_FOR_LOADER }}

    if: github.repository == 'lin-stephanie/astro-antfustyle-theme'
    runs-on: ${{ matrix.os }}
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set node ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🛠️ Run astro check
        run: pnpm check

      - name: 🔎 Check code lint
        run: pnpm lint

      - name: 🚀 Build the project
        run: pnpm build

      - name: ✅ Verify build output
        shell: bash
        run: |
          if [ ! -d "./dist" ]; then
            echo "Build failed: dist directory not found."
            exit 1
          else
            echo "Build succeeded! 🎉"
          fi
