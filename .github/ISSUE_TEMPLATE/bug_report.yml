name: Bug Report
description: Report an issue with Astro AntfuStyle Theme
labels: ['bug']
title: 'Bug: '
body:
  - type: markdown
    attributes:
      value: Thanks for taking the time to fill out this bug report!
  - type: textarea
    id: what-happened
    attributes:
      label: Bug description
      description: A clear and concise description of what the bug is. If the bug affects the UI, please upload screenshots to help clarify the issue.
    validations:
      required: true
  - type: textarea
    id: reproduce
    attributes:
      label: Steps to reproduce
      description: The more detail you provide, the easier it will be to narrow down and fix the bug.
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What should happen?
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: Relevant errors (if available)
      description: Open the browser's Developer Console by pressing `CTRL + SHIFT + I` (Windows) or `Command + Option + I` (MacOS). If there are any errors or warnings in the console, please copy and paste them here.
    validations:
      required: false
  - type: textarea
    id: environment
    attributes:
      label: Environment details
      description: Specify the operating systems and browsers where this bug occurs (run `npx envinfo --system --binaries --browsers`).
      placeholder: OS (Windows, Linux, macOS, iOS, Android), Browser (Chrome, Edge, Firefox, Safari, Opera...), Version (1.0.0)
    validations:
      required: true
  - type: checkboxes
    id: validations
    attributes:
      label: Before Submitting
      options:
        - label: Check that there isn't [already an issue](https://github.com/lin-stephanie/astro-antfustyle-theme/issues) that reports the same bug to avoid creating a duplicate.
          required: true
        - label: Check that this is a concrete bug. For Q&A open a [GitHub Discussion](https://github.com/lin-stephanie/astro-antfustyle-theme/discussions) or refer to the [posts on the live demo](https://astro-antfustyle-theme.vercel.app/blog/).
          required: true
