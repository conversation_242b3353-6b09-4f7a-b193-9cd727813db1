@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

main {
  --primary-default: 17, 187, 130;
  --bg-default: 22, 22, 24;
  --color-prettylights-syntax-comment: #8b949e;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-variable: #ffa657;
  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
  --color-prettylights-syntax-carriage-return-text: #f0f6fc;
  --color-prettylights-syntax-carriage-return-bg: #b62324;
  --color-prettylights-syntax-string-regexp: #7ee787;
  --color-prettylights-syntax-markup-list: #f2cc60;
  --color-prettylights-syntax-markup-heading: #1f6feb;
  --color-prettylights-syntax-markup-italic: #c9d1d9;
  --color-prettylights-syntax-markup-bold: #c9d1d9;
  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
  --color-prettylights-syntax-markup-deleted-bg: #67060c;
  --color-prettylights-syntax-markup-inserted-text: #aff5b4;
  --color-prettylights-syntax-markup-inserted-bg: #033a16;
  --color-prettylights-syntax-markup-changed-text: #ffdfb6;
  --color-prettylights-syntax-markup-changed-bg: #5a1e02;
  --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
  --color-prettylights-syntax-markup-ignored-bg: #1158c7;
  --color-prettylights-syntax-meta-diff-range: #d2a8ff;
  --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
  --color-btn-text: rgb(*********** / 86%);
  --color-btn-bg: rgba(var(--bg-default), 1);
  --color-btn-border: rgba(var(--bg-default), 1);
  --color-btn-shadow: 0 1px 0 rgba(var(--bg-default), 1);
  --color-btn-inset-shadow: inset 0 1px 0 rgba(var(--bg-default), 1);
  --color-btn-hover-bg: rgba(var(--bg-default), 0.5);
  --color-btn-hover-border: rgba(var(--bg-default), 0.5);
  --color-btn-active-bg: rgba(var(--primary-default), 0.2);
  --color-btn-active-border: rgba(var(--primary-default), 1);
  --color-btn-selected-bg: rgba(var(--primary-default), 0.15);
  --color-btn-primary-text: rgb(*********** / 100%);
  --color-btn-primary-bg: rgba(var(--primary-default), 0.45);
  --color-btn-primary-border: rgba(var(--primary-default), 0.5);
  --color-btn-primary-shadow: 0 1px 0 rgb(27 31 36 / 10%);
  --color-btn-primary-inset-shadow: inset 0 1px 0 hsl(0deg 0% 100% / 3%);
  --color-btn-primary-hover-bg: rgba(var(--primary-default), 0.53);
  --color-btn-primary-hover-border: rgba(var(--primary-default), 0.75);
  --color-btn-primary-selected-bg: rgba(var(--primary-default), 0.45);
  --color-btn-primary-selected-shadow: inset 0 1px 0 rgb(0 45 17 / 20%);
  --color-btn-primary-disabled-text: rgb(*********** / 80%);
  --color-btn-primary-disabled-bg: rgba(var(--primary-default), 0.5);
  --color-btn-primary-disabled-border: rgba(var(--primary-default), 0.5);
  --color-action-list-item-default-hover-bg: rgb(177 186 196 / 12%);
  --color-segmented-control-bg: rgb(110 118 129 / 10%);
  --color-segmented-control-button-bg: #0d1117;
  --color-segmented-control-button-selected-border: rgba(
    var(--bg-default),
    0.85
  );
  --color-fg-default: rgb(*********** / 86%);
  --color-fg-muted: rgb(*********** / 60%);
  --color-fg-subtle: rgb(*********** / 50%);
  --color-canvas-default: rgb(0 0 0 / 100%);
  --color-canvas-overlay: rgb(0 0 0 / 100%);
  --color-canvas-inset: rgb(0 0 0 / 100%);
  --color-canvas-subtle: rgb(0 0 0 / 100%);
  --color-border-default: rgba(var(--bg-default), 0.85);
  --color-border-muted: rgb(175 184 193 / 20%);
  --color-neutral-muted: rgb(175 184 193 / 20%);
  --color-accent-fg: rgba(var(--primary-default), 0.85);
  --color-accent-emphasis: rgba(var(--primary-default), 0.95);
  --color-accent-muted: rgba(var(--primary-default), 0.4);
  --color-accent-subtle: rgba(var(--primary-default), 0.1);
  --color-success-fg: #3fb950;
  --color-attention-fg: #d29922;
  --color-attention-muted: rgb(187 128 9 / 40%);
  --color-attention-subtle: rgb(187 128 9 / 15%);
  --color-danger-fg: #f85149;
  --color-danger-muted: rgb(248 81 73 / 40%);
  --color-danger-subtle: rgb(248 81 73 / 10%);
  --color-primer-shadow-inset:
    0 1px 0 rgba(var(--bg-default), 1), inset 0 1px 0 rgba(var(--bg-default), 1);
  --color-scale-gray-7: rgb(22 22 24 / 100%);
  --color-scale-blue-8: rgb(16 185 129 / 15%);
  --color-social-reaction-bg-hover: var(--color-scale-gray-7);
  --color-social-reaction-bg-reacted-hover: var(--color-scale-blue-8);
}

/* Aligns the font family with the sans setting */
main {
  font-family:
    'Inter',
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
}

main .pagination-loader-container {
  background-image: url('https://github.com/images/modules/pulls/progressive-disclosure-line-dark.svg');
}

main .gsc-loading-image {
  background-image: url('https://github.githubassets.com/images/mona-loading-dark.gif');
}

/* Hide reaction count and comment header */
.gsc-reactions-count,
.gsc-header {
  display: none;
}

/* Set border color for reacted social reaction button */
.gsc-direct-reaction-button.gsc-social-reaction-summary-item.has-reacted {
  border-color: var(--color-border-default);
}

/* Inherit text color for comment author avatar */
.gsc-comment-author-avatar {
  color: inherit;
}

/* Override link color on hover for primary links */
.link-primary.overflow-hidden.text-ellipsis:hover {
  color: var(--color-fg-default);
}

/* Remove underline and adjust spacing for secondary links */
.link-secondary.overflow-hidden.text-ellipsis {
  text-decoration-line: none;
  word-spacing: -2px;
  &:hover {
    color: var(--color-fg-default);
  }
}

/* Remove box shadow and border from primary buttons */
.btn-primary,
.btn.ml-1.rounded-md.border {
  box-shadow: none;
  border: none;
}
