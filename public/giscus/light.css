@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

main {
  --primary-default: 8, 155, 91;
  --bg-default: 246, 246, 247;
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-btn-text: #24292f;
  --color-btn-bg: rgba(var(--bg-default), 1);
  --color-btn-border: rgba(var(--bg-default), 1);
  --color-btn-shadow: 0 1px 0 rgba(var(--bg-default), 1);
  --color-btn-inset-shadow: inset 0 1px 0 rgba(var(--bg-default), 1);
  --color-btn-hover-bg: rgba(var(--bg-default), 0.5);
  --color-btn-hover-border: rgba(var(--bg-default), 0.5);
  --color-btn-active-bg: rgba(var(--primary-default), 0.2);
  --color-btn-active-border: rgba(var(--primary-default), 1);
  --color-btn-selected-bg: rgba(var(--primary-default), 0.15);
  --color-btn-primary-text: rgb(*********** / 100%);
  --color-btn-primary-bg: rgba(var(--primary-default), 1);
  --color-btn-primary-border: rgba(var(--primary-default), 1);
  --color-btn-primary-shadow: 0 1px 0 rgb(31 35 40 / 10%);
  --color-btn-primary-inset-shadow: inset 0 1px 0 rgb(*********** / 3%);
  --color-btn-primary-hover-bg: rgba(var(--primary-default), 0.9);
  --color-btn-primary-hover-border: rgba(var(--primary-default), 0.75);
  --color-btn-primary-selected-bg: rgba(var(--primary-default), 1);
  --color-btn-primary-selected-shadow: inset 0 1px 0 rgb(0 45 17 / 20%);
  --color-btn-primary-disabled-text: rgb(*********** / 80%);
  --color-btn-primary-disabled-bg: rgba(var(--primary-default), 0.5);
  --color-btn-primary-disabled-border: rgba(var(--primary-default), 0.5);
  --color-action-list-item-default-hover-bg: rgb(208 215 222 / 32%);
  --color-segmented-control-bg: #eaeef2;
  --color-segmented-control-button-bg: #fff;
  --color-segmented-control-button-selected-border: rgba(
    var(--bg-default),
    0.85
  );
  --color-fg-default: rgb(60 60 67);
  --color-fg-muted: rgb(60 60 67 / 75%);
  --color-fg-subtle: rgb(60 60 67 / 33%);
  --color-canvas-default: rgb(***********);
  --color-canvas-overlay: rgb(***********);
  --color-canvas-inset: rgb(***********);
  --color-canvas-subtle: rgb(***********);
  --color-border-default: rgba(var(--bg-default), 0.85);
  --color-border-muted: rgb(175 184 193 / 20%);
  --color-neutral-muted: rgb(175 184 193 / 20%);
  --color-accent-fg: rgba(var(--primary-default), 0.85);
  --color-accent-emphasis: rgba(var(--primary-default), 0.95);
  --color-accent-muted: rgba(var(--primary-default), 0.4);
  --color-accent-subtle: rgba(var(--primary-default), 0.1);
  --color-success-fg: #1a7f37;
  --color-attention-fg: #9a6700;
  --color-attention-muted: rgb(212 167 44 / 40%);
  --color-attention-subtle: #fff8c5;
  --color-danger-fg: #d1242f;
  --color-danger-muted: rgb(255 129 130 / 40%);
  --color-danger-subtle: #ffebe9;
  --color-primer-shadow-inset:
    0 1px 0 rgba(var(--bg-default), 1), inset 0 1px 0 rgba(var(--bg-default), 1);
  --color-scale-gray-1: rgb(234 238 242 / 100%);
  --color-scale-blue-1: rgb(16 185 129 / 15%);
  --color-social-reaction-bg-hover: var(--color-scale-gray-1);
  --color-social-reaction-bg-reacted-hover: var(--color-scale-blue-1);
}

/* Aligns the font family with the sans setting */
main {
  font-family:
    'Inter',
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
}

main .pagination-loader-container {
  background-image: url('https://github.com/images/modules/pulls/progressive-disclosure-line.svg');
}

main .gsc-loading-image {
  background-image: url('https://github.githubassets.com/images/mona-loading-default.gif');
}

.gsc-comment:not(.gsc-reply-box) .gsc-replies {
  border-radius: unset;
}

/* Hide reaction count and comment header */
.gsc-reactions-count,
.gsc-header {
  display: none;
}

/* Set border color for reacted social reaction button */
.gsc-direct-reaction-button.gsc-social-reaction-summary-item.has-reacted {
  border-color: var(--color-border-default);
}

/* Inherit text color for comment author avatar */
.gsc-comment-author-avatar {
  color: inherit;
}

/* Override link color on hover for primary links */
.link-primary.overflow-hidden.text-ellipsis:hover {
  color: var(--color-fg-default);
}

/* Remove underline and adjust spacing for secondary links */
.link-secondary.overflow-hidden.text-ellipsis {
  text-decoration-line: none;
  word-spacing: -2px;
  &:hover {
    color: var(--color-fg-default);
  }
}

/* Remove box shadow and border from primary buttons */
.btn-primary,
.btn.ml-1.rounded-md.border {
  box-shadow: none;
  border: none;
}
