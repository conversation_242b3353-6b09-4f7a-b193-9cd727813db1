---
interface Props {
  title?: string
  subtitle?: string
  isSearchable?: boolean
  isCentered?: boolean
  class?: string
}

const {
  title,
  subtitle,
  isSearchable = false,
  isCentered = false,
  class: className,
} = Astro.props
---

<header
  class:list={{
    'prose mx-auto mb-8': true,
    'text-center': isCentered,
  }}
>
  {
    title && (
      <h1
        data-pagefind-body={isSearchable ? true : undefined}
        data-pagefind-weight={isSearchable ? '10' : undefined}
        data-pagefind-meta={isSearchable ? 'title' : undefined}
      >
        {title}
      </h1>
    )
  }
  {subtitle && <p class="mt--4! op-50 italic">{subtitle}</p>}
  <slot name="head" />
</header>
{
  Astro.slots.has('article') && (
    <article
      class="slide-enter-content prose mx-auto"
      data-pagefind-body={isSearchable ? true : undefined}
    >
      <slot name="article" />
    </article>
  )
}
{
  Astro.slots.has('default') && (
    <div class:list={['mx-auto', className]}>
      <slot />
    </div>
  )
}
