---
title: 本项目blog.md到html展示的过程
description: 本项目blog.md到html展示的过程
pubDate: 2017-09-01
lastModDate: ''
toc: true
share: false
giscus: true
ogImage: false
draft: true
---

### 总览

- 内容来源：`src/content/blog/**.md|mdx` 被定义为 `blog` 内容集合，并由 Zod schema 校验 frontmatter。
- 构建期生成：`/blog` 列表页由 `src/pages/blog/index.mdx` 渲染；每篇文章详情页按文件路径生成动态路由 `/blog/<slug>`。
- 渲染核心：使用 `astro:content` 的 `getCollection()` 拉取集合，`render(entry)` 将 Markdown 转为可渲染的 `Content` 组件、目录 `headings` 等。
- Markdown/MDX 流水线：在 `astro.config.ts` 里配置一组 remark/rehype 插件（包括自定义阅读时长与 OG Image 生成等），在渲染时生效。
- 生产环境过滤：生产构建会自动过滤 `draft: true` 的文章。

### 内容集合定义

- 在 `src/content.config.ts` 定义 `blog` 集合（glob loader + `postSchema`）：

```js
// 1:34:virtual-venus/src/content.config.ts
const blog = defineCollection({
  loader: glob({ base: './src/content/blog', pattern: '**/[^_]*.{md,mdx}' }),
  schema: postSchema,
})
```

- `postSchema` 规定文章 frontmatter（标题、日期、toc/分享/评论开关、draft 等）：

```js
// 45:131:virtual-venus/src/content/schema.ts
export const postSchema = z.object({
  title: z.string().max(60),
  ...
  pubDate: z.coerce.date(),
  lastModDate: z.union([z.coerce.date(), z.literal('')]).optional(),
  minutesRead: z.number().optional(),
  radio: z.boolean().default(false),
  video: z.boolean().default(false),
  platform: z.string().default(''),
  toc: z.boolean().default(true),
  share: z.boolean().default(true),
  giscus: z.boolean().default(true),
  ogImage: z.union([z.string(), z.boolean()]).default(true),
  redirect: z.string().url('Invalid url.').optional(),
  draft: z.boolean().default(false),
})
```

### 列表页 `/blog`

- 页面位置：`src/pages/blog/index.mdx` → 使用 `ListView` 渲染 `blog` 集合。

```
// 14:26:virtual-venus/src/pages/blog/index.mdx
<BaseLayout ...>
  <StandardLayout ...>
    <ListView collectionType="blog" pageToc={frontmatter.toc} />
  </StandardLayout>
</BaseLayout>
```

- `ListView` 内部：按集合类型拉取、过滤、排序，并按年份分组展示；用 `render(item)` 拿到计算的 `minutesRead`。

```js
// 21:49:virtual-venus/src/components/views/ListView.astro
const { collectionType, pageToc } = Astro.props
...
if (collectionType === 'blog' || collectionType === 'changelog') {
  const blogItems = await getFilteredPosts(collectionType)
  sortedBlogItems = getSortedPosts(blogItems)
  ...
}
```

```js
// 107:136:virtual-venus/src/components/views/ListView.astro
const { data, id } = item
const { remarkPluginFrontmatter } = await render(item)
const minutesRead = data.minutesRead || remarkPluginFrontmatter.minutesRead
...
<ListItem ... postSlug={id} title={data.title} date={data.pubDate} {minutesRead} ... />
```

- 过滤与排序在工具模块完成：

```js
// 24:39:virtual-venus/src/utils/data.ts
export async function getFilteredPosts(collection: 'blog' | 'changelog') {
  return await getCollection(collection, ({ data }) => {
    return import.meta.env.PROD ? !data.draft : true
  })
}
export function getSortedPosts(posts) {
  return posts.sort((a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf())
}
```

### 详情页 `/blog/<slug>`

- 动态路由：`src/pages/blog/[...slug].astro` 在构建期用 `getStaticPaths()` 为每篇文章生成路径，`slug` 来自集合 `post.id`（等于相对路径，支持多级目录）。

```js
// 5:12:virtual-venus/src/pages/blog/[...slug].astro
export async function getStaticPaths() {
  const posts = await getFilteredPosts('blog')
  return posts.map((post) => ({
    params: { slug: post.id },
    props: { post },
  }))
}
```

- 渲染：传入 `post` 给 `RenderPost` 组件。

```js
// 14:17:virtual-venus/src/pages/blog/[...slug].astro
const { post } = Astro.props
<RenderPost {post} isSearchable={true} isCommentable={true} />
```

- `RenderPost` 将 Markdown 渲染为 `Content`，并按 frontmatter 与全局 FEATURES 开关展示 TOC、分享、评论等。

```js
// 25:41:virtual-venus/src/components/views/RenderPost.astro
const { data: frontmatter } = post
const { Content, headings, remarkPluginFrontmatter } = await render(post)
...
const minutesRead = frontmatter.minutesRead || (remarkPluginFrontmatter.minutesRead as number)
const tocEnabled = Array.isArray(toc) && toc[0] && frontmatter.toc
const shareEnabled = Array.isArray(share) && share[0] && frontmatter.share
const giscusEnabled = isCommentable && Array.isArray(giscus) && giscus[0] && frontmatter.giscus
```

```js
// 55:57:virtual-venus/src/components/views/RenderPost.astro
{tocEnabled && <Toc {headings} />}
<Content />
```

### Markdown/MDX 转换流水线

- 在 `astro.config.ts` 中设置全局的 remark/rehype 插件，配合 `@astrojs/mdx` 处理 `.mdx`：

```js 
// 22:26:virtual-venus/astro.config.ts
markdown: {
  syntaxHighlight: false,
  remarkPlugins,
  rehypePlugins,
},
```

- 插件列表在 `plugins/index.ts`，包括：
  - 自定义 `remark-reading-time` 计算阅读时长（供 `render(entry)` 时的 `remarkPluginFrontmatter.minutesRead` 用）。
  - `remark-generate-og-image`，以及数学、图片属性、指令等插件；rehype 里有 heading id、新窗口外链属性、锚点等。

```js
// 22:61:virtual-venus/plugins/index.ts
export const remarkPlugins = [
  remarkDirective, remarkDirectiveSugar, remarkImgattr, remarkMath,
  remarkReadingTime,
  ...(Array.isArray(FEATURES.ogImage) && FEATURES.ogImage[0] ? [remarkGenerateOgImage] : []),
]
export const rehypePlugins = [
  [rehypeHeadingIds, { headingIdCompat: true }],
  rehypeKatex, [rehypeCallouts, { theme: 'vitepress' }],
  [rehypeExternalLinks, { ... }],
  [rehypeAutolinkHeadings, { behavior: 'append', ... }],
]
```

### 结论（从 md 到网页）

- 将 Markdown/MDX 放入 `src/content/blog`，其 frontmatter 会被 `postSchema` 校验。
- 构建期通过 `getCollection('blog')` 收集并过滤（生产环境排除 `draft`），列表页用 `ListView` 展示，详情页通过 `[...slug].astro` 的 `getStaticPaths()` 生成每篇页面。
- 渲染时 `render(entry)` 把 Markdown 转为可渲染的 `Content` 组件，并输出目录与插件产物（如阅读时长、数学公式、外链处理等），再由布局组件输出最终 HTML。

- 若新增文章，只需在 `src/content/blog/` 新建 `.md|.mdx`，按 `postSchema` 填好 frontmatter，即会出现在 `/blog` 列表，并生成对应详情页路由。

- **要点**：`post.id` 即文章相对路径作为 slug；支持嵌套目录；生产环境会隐藏 `draft: true`。

- 可进一步参考本文档页内置的结构表述（同仓库的一篇文档里已有映射说明）。

- 如需查看更多插件细节（如阅读时长如何写入 frontmatter），可打开 `plugins/remark-reading-time.ts` 查看实现。
