---
title: 使用nvm作为管理node的版本工具，在项目中使用了ghooks的代码校验，使用sourcetree提交代码报错
description: 使用nvm作为管理node的版本工具，在项目中使用了ghooks的代码校验，使用sourcetree提交代码时，提示：env： node： No such file or directory
pubDate: 2024-07-10
lastModDate: 2024-07-10
toc: true
share: false
giscus: true
ogImage: false
draft: false
---

如题：使用nvm作为管理node的版本工具，在项目中使用了ghooks的代码校验，使用sourcetree提交代码时，提示：

```
    env: node: No such file or directory
```

### 创建软链接

将 Node.js 的可执行文件链接到系统的全局路径（如 /usr/local/bin）。运行以下命令：

```
    sudo ln -s $(which node) /usr/local/bin/node
```

如果需要为 npm 和 npx 也创建软链接，可以运行：

```
    sudo ln -s $(which npm) /usr/local/bin/npm
    sudo ln -s $(which npx) /usr/local/bin/npx
```

###步骤 3：验证软链接是否生效

运行以下命令检查软链接是否成功：

```
    node -v
    npm -v
    npx -v
```

如果显示版本号，则说明软链接已正确设置。

Tips：
1、修改完后，重启终端、sourceTree。
2、创建软链接时，使用nvm use xx 切换版本后，再执行以上命令，即可指定软链接的版本。
