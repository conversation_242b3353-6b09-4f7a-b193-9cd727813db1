---
title: '🔗 1.4.0: TOC Highlight, Customize External Link Behavior'
description: Release Astro AntfuStyle Theme 1.4.0
pubDate: 2025-01-25
toc: true
share: true
ogImage: false
---

### 🚀 Features

- Improve icon resolution in `:link` directive ([#18](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/18))
- **custom:** Add `UI.externalLink` option to customize external link behavior ([#15](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/15))
- **mobile:** Support disable panel with `Escape` key  & optimize backdrop handling ([923b040](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/923b040))
- **share:** Add configuration to support sharing posts to <PERSON><PERSON> ([a3a123e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/a3a123e))
- **toc:** Auto-highlight TOC item based on browsing position ([#13](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/13))

### 🐞 Bug Fixes

- :::details
  ::summary[Remove `rehype-raw` as it causes some functionalities of `astro-expressive-code` integration to fail ([#15](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/15))] 
  - Resolve trailing spaces caused by parsing issues affecting stylin
  - Fix cursorType only applied when newTab: true
  - Update `astro-expressive-code` config (`ec.config.mjs`)
  - Tweak styles
  :::

### 💅 Refactors

- Switch to mobile style at widths < 1128px instead of 1024px ([14d4fe5](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/14d4fe5))

### 🏡 Chore

- Update docs ([6757d53](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/6757d53))
- Set external links to open in same tab & remove unused styles and minor tweaks ([3816e53](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/3816e53))
- Update deps & update docs ([b6387e3](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b6387e3))

### ❤️ Contributors

- YCG ([@y-cg](http://github.com/y-cg))
- Stephanie Lin ([@lin-stephanie](http://github.com/lin-stephanie))

[View changes on GitHub](https://github.com/lin-stephanie/astro-antfustyle-theme/compare/1.3.0...1.4.0)
