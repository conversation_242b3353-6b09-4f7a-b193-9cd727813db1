---
title: 🎉 Release 1.0.0 ✨ 
description: Release Astro AntfuStyle Theme 1.0.0
pubDate: 2024-10-06
toc: true
share: true
ogImage: false
---


### 🚀 Features

- **nav:**
  - :::details
    ::summary[Add navigation bar and page navigation ([6ecfb4c](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/6ecfb4c))]
    - use json schema for blog configs 
    - add ToTopButton, Link, NavBar, and global.css 
    - modify main layout structure and styles 
    - configure to use presetIcon 
    - add safelist (unocss.config.ts) to handle runtime styles 
    - modify path alias (tsconfig.json), presetAttributify prefix (unocss.config.ts) settings, and env.d.ts 
    - add .editorconfig to control vscode tabs (to match "tabWidth": 2 in prettier) 
    :::
  - Add BackLink ([857dfb0](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/857dfb0))
- **page:**
  - :::details
    ::summary[Add /blog and /blog/[slug] pages ([b1f9a7b](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b1f9a7b))]
    - define postsSchema and configure blog collection 
    - add PostLayout, Categorizer, PostItem 
    - add utils 
    - update global style files 
    - modify Link 
    - update env.d.ts (run astro sync) & eslint @typescript-eslint/triple-slash-reference 
    :::
  - :::details
    ::summary[Add /projects page ([fe2df75](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/fe2df75))]
    - define projectsSchema and configure projects collection
    - add ProjectGroup, WideLayout, data.json
    - modify Categorizer (add needId, wide props)
    - add slug, extractIconsStartingWithI, loadIconComponent utilities
    - add projectIcons to safelist (unocss.config.ts)
    :::
  - Add /changelog ([77ed19f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/77ed19f))
- **theme:**
  - :::details
    ::summary[Add light/dark mode with view transitions api ([1fd5c3e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/1fd5c3e))]
    - add ToggleTheme (listens to 'astro:after-swap', 'astro:page-load')
    - update ToTopButton (listens to 'astro:page-load')
    - fix PostLayout styles
    - update utils type declarations
    :::
  - Support system dark/light mode responsiveness (including logic to control theme-color meta tag) & improve     a11y for theme switch ([7c0c027](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/7c0c027))
    :::
- **style:**
  - Configure font, slide enter rule ([b959be2](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b959be2))
  - Add 'op-transition' utility for consistent opacity transitions & unify subtitle opacity in layouts ([33177be](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/33177be))
  - Adjust styles for code and details elements in prose ([43adf86](https://github.com/lin-stephanie/ astro-antfustyle-theme/commit/43adf86))
  - Update styles for code, table, and callout elements in prose ([54fbd0a](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/54fbd0a))
- **astro:**
  - :::details
    ::summary[Add rehype-autolink-headings & shikiConfig.themes ([2cc9c7a](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/2cc9c7a))]
    - configure rehype-autolink-headings plugin (add links to headings themselves)
    - configure shiki code themes and adjust markdown.css
    - modify presetAttributify.prefixedOnly to false (unocss.config.ts)
    :::
  - Add rehype-external-links ([46ae647](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/46ae647))
  - :::details
    ::summary[Add rehype-callouts ([3dde001](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/3dde001))]
    - add callout styles (markdown.css)
    - modify proseWrap (.prettierrc)
    :::
  - :::details
    ::summary[Enable experimental features in 4.14: contentLayer, contentIntellisense ([5533699](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/5533699))]
    - add pageSchema and loader-based 'pages' collection
    - test schema-driven frontmatter restrictions and suggestions in pages/blog/index.mdx
    :::
  - Support for saving blog posts in nested directories ([f649c9e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f649c9e))
  - Support KaTeX Math in Markdown/MDX ([c8c7ac3](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c8c7ac3))
  - Support subpath deployment (simply configure the base option) ([bd9b059](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/bd9b059))
- **bg:**
  - :::details
    ::summary[Add ArtPlum & Bckground ([dd6ee89](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/dd6ee89))]
    - adjust the structure of PostLayout
    - add types.ts, useRafFn, useWindowSize
    :::
  - :::details
    ::summary[Add Dots ([30d1d99](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/30d1d99))]
    - Install p
    - modify Background (only conditionally return components)
    - modify Plum (constant names, component template)
    :::
  - :::details
    ::summary[Add Rose ([f670c81](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f670c81))]
    - add getRandom, getRandomPercentage utilities
    - rename Dots to Dot
    :::
  - Add Particle ([5cea2f0](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/5cea2f0))
  - :::details
    ::summary[Modify Plum (formerly ArtPlum) ([16a9b79](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/16a9b79))]
    - add pages.<page>.bgType custom option (types.ts)
    - remove hooks/
    - modify BaseLayout, Background, pages/index
    :::
- **loading:**
  - :::details
    ::summary[Add nprogress ([slug].astro) ([af503e6](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/af503e6))]
    - change component name
    - filter out posts where data.draft is true
    :::
- **header:**
  - Add NavTabs ([f64f213](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f64f213))
  - :::details
    ::summary[Extract post header content into Header ([6306117](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/6306117))]
    - add subtitle field to postsSchema
    - self-closing div tags not supported in .md by default
    :::
- **share:**
  - :::details
    ::summary[Add ShareLink ([834455f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/834455f))]
    - add share functionality on the /blog/[slug] page
    - add custom configuration for features.share
    - modify BaseLayout, BackLink
    :::
- **search:**
  - :::details
    ::summary[Add SearchSwitch ([0b1f647](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/0b1f647))]
    - install pagefind to enable searching posts
    - add CSS variable
    - add getUrl, setClickOutsideToClose utilities
    - add vite.base (astro.config.ts), shortcuts-'shadow-c' (unocss.config.ts)
    :::
- **toc:**
  - :::details
    ::summary[Add Toc, TocItem ([3557ab8](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/3557ab8))]
    - add toc on blog/[slug] and /projects pages (ensure frontmatter has higher priority than config.ts)
    - add generateToc utilities
    - modify the styling of the Toc (markdown.css)
    - modify ToTopButton style (ensure alignment with the right-side layout's toc and higher z-index than toc)
    - add postSchema, projectsSchema fields (toc, share)
    - add features.toc custom configuration
    - change @apply to --uno: ''
    :::
  - Implement smooth scrolling ([12742dc](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/12742dc))
  - Support controlling TOC visibility on each page ([2e0a324](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/2e0a324))
- **schema:**
  - :::details
    ::summary[Handle draft, data fields ([b9cbc40](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b9cbc40))]
    - default draft to false.
    - modify formatDate, /blog
    - apply .toISOString() to data fields (StandardLayout)
    :::
  - Update content zod schemas & add vscode snippets ('projectData', 'streamData') ([4046fd8](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/4046fd8))
- **code:**
  - :::details
    ::summary[Configure astro-expressive-code integration ([c9fc803](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c9fc803))]
    - implement syntax highlighting, code copying, editor & terminal frames, line markers, word wrap, collapsible sections, line numbers
    - install @expressive-code/plugin-collapsible-sections, @expressive-code/plugin-line-numbers
    - disable Astro’s syntax highlight
    - adjust built-in styles of Expressive Code
    :::
- **image:**
  - :::details
    ::summary[Support adding 'figure' and 'a' container to 'img' generated from Markdown syntax & support for custom img attrs ([c16c84b](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c16c84b))]
    - add custom remarkImageDirective plugin
    - add "postinstall": "pnpm add -D sharp" script
    :::
  - :::details
    ::summary[Support image zoom preview within posts ([f06ce03](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f06ce03))]
    - install medium-zoom and import the corresponding styles
    - move the script from Head.astro to BaseLayout.astro
    - adjust z-index
    :::
- **og-image:**
  - :::details
    ::summary[Support automatic generation of og image (based on svg template) ([f2eb657](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f2eb657))]
    - add custom remarkGenerateOgImage plugin
    - modify and add ogImage field (schema.ts)
    - obtain and pass remarkPluginFrontmatter.ogImage In /blog/[slug]
    - modify ogImageURL assignment logic
    - add getCurrentFormattedTime utilities
    :::
  - :::details
    ::summary[Support og image customization via html and css ([ee030d4](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/ee030d4))]
    - update remark-generate-og-image plugin logic
    - add markup.ts for ReactNode creation
    - add base
    - ts for background encoding
    - og-image.png as the fallback
    :::
  - Support lossless compression for auto-generated images & fix accidental HTML entity escaping in 'satori-html' ([5b10047](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/5b10047))
- **customize:**
  - :::details
    ::summary[Add features.ogImage config & support regenerating fallback og image ([937f496](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/937f496))]
    - add customizable ogImage options: authorOrBrand, fallbackTitle, fallbackBgType
    - modify the ogImageURL assignment logic
    - update remark-generate-og-image plugin logic
    :::
  - Refactor to UI configuration & add custom configurations (navBarLayout, showGroupItemColorOnHover, toc.minHeadingLevel, toc.maxHeadingLevel, toc.displayMode) ([94dbb51](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/94dbb51))
- **frontmatter:**
  - Add lastModified field, rename date to created ([8cacb6f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/8cacb6f))
  - :::details
    ::summary[Add support for auto-calculating post reading time ([84e7b33](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/84e7b33))]
    - rename duration to minutesRead, change to optional number type
    - add custom remark-reading-time plugin
    - update rendering logic for minutesRead
    :::
  - Support for ogImage set to true to auto-generate OG image & add vscode snippets ('pageFrontmatter') ([6af984e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/6af984e))
- **share:**
  - :::details
    ::summary[Support multiple shareable platforms ([0399e10](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/0399e10))]
    - fix the issue of rendering extra whitespace
    :::
  - Support custom sharing of posts via Twitter, Mastodon, Facebook, Pinterest, Reddit, Telegram, WhatsApp, and Email ([c4546e1](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c4546e1))
- **mdx:**
  - :::details
    ::summary[Support for using MDX ([62e3633](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/62e3633))]
    - move pages/blog/index.astro to components/ and rename to PostList.astro as a reusable Astro component
    - write pages/blog/index.mdx to replace pages/blog/index.astro
    :::
- **snippet:**
  - Support for quickly inserting editable frontmatter for Markdown/MDX in VSCode ([6a21fd9](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/6a21fd9))
- **directive:**
  - :::details
    ::summary[Support markdown syntax in figcaption text for formatting ([361bf00](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/361bf00))]
    :::
  - :::details
    ::summary[Support adding styled links in Markdown/MDX with `:link`, including simplified syntax for linking to GitHub users/repos ([eafbb23](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/eafbb23))]
    - modify remark-directive-sugar plugin (implemented 'button-s', 'button-r', 'github-link' style types)
    :::
  - Support for customizable badge-like representations using `:badge/:badge-*` in Markdown/MDX & regular remark-directive usage ([aa69a4e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/aa69a4e))
  - :::details
    ::summary[Support embedding videos in Markdown/MDX using the ::video directive ([3610160](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/3610160))]
    - add custom remark-directive-sugar plugin
    - modify error handling in the remark-image-container plugin
    :::
- **logo:**
  - :::details
    ::summary[Support custom logo ([6f9517c](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/6f9517c))]
    - add styles for SVG animation
    - refactor NavBar layout implementation
    :::
- **pwa:**
  - Update icon files & add manifest.webmanifest support for PWA ([c37ec77](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c37ec77))
- **seo:**
  - :::details
    ::summary[Dynamically adjust OG display type & add Google JSON-LD Structured Data ([ab2f1a4](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/ab2f1a4))]
    - support og:type as article for posts (when pubDate exists), otherwise use website
    - rename created to pubDate and lastModified to lastModDate
    :::
  - Integrate automatic generation of sitemap.xml and robots.txt during build ([7f2d98c](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/7f2d98c))
- **rss:**
  - Support for visitors subscribing to the blog ([bce7cee](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/bce7cee))
- **footer:**
  - Update content, auto-fetch current year for copyright, improve a11y ([aa16ca2](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/aa16ca2))
- **refactor:**
  - Adjust BaseLayout to keep footer at the viewport bottom ([fa49945](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/fa49945))
- **a11y:**
  - Add role, aria-label, and aria-current attributes ([ab82ecd](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/ab82ecd))


### 🐞 Bug Fixes

- **nav:**
  - :::details
    ::summary[Resolve navigation path parsing errors post-build ([48a2061](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/48a2061))]
    - add ensureTrailingSlash utility (enforce trailing slashes on all paths)
    - refactor layout templates
    :::
- **search:**
  - :::details
    ::summary[Make search results scrollable (with invisible scrollbar) ([b608816](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b608816))]
    - modify /* Scrollbar */ style (main.css)
    :::
- **a11y:**
  - Improve accessibility (Sa11y audit) ([84c1456](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/84c1456))
- **view:**
  - Prevent text overflow on /projects at 1024px width & rename custom option (UI.groupItemCols to UI.maxGroupColumns) ([b78fad4](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b78fad4))
  - Resolve responsive issues in footer and other view adjustments ([f1716aa](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f1716aa))
- **bg:**
  - Prevent premature p5 access by custom elements & support bgType set to false to disable background ([2b0d8ba](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/2b0d8ba))
- **directive:**
  - Correct inaccurate GITHUB_REPO_REGEXP regex matching & add .prettierignore (exclude *.md from being formatted by prettier) ([c7ffe8f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c7ffe8f))

### 💅 Refactors

- **customize:**
  - Change json schema to ts ([5afa371](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/5afa371))
- **page:**
  - :::details
    ::summary[Replace .astro with .mdx for generating page files (/feeds, /streams) ([9eafc7f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/9eafc7f))]
    - refactor PostList to List, PostItem to ListItem (for list views)
    - add 'streams', 'feeds', 'changelog' collections
    - modify TabbedLayout
    - add LAYOUTS custom configuration
    - Install @ascorbic/feed-loader for external feeds
    :::
  - :::details
    ::summary[Replace .astro with .mdx for generating page files (/projects) ([d9fb2c4](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/d9fb2c4))]
    - refactor ProjectGroup to GroupItem, Group (for group views)
    - refactor pages/projects/index.astro to pages/projects.mdx
    - modify WideLayout, projectsSchema
    - add and implement groupItemCols  custom configuration
    :::
  - :::details
    ::summary[Replace .astro with .mdx for generating page files (/blog、404) ([ae7c044](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/ae7c044))]
    - refactor StandardLayout and blog/[slug] (cannot refactor to .mdx due to dynamic routing)
    - adjust bgType fetching logic, use dirname when filenama is 'index' (remark-generate-og-image.ts)
    - replace TabbedLayout with StandardLayout on blog/ page
    :::
  - Add RenderPost for generating a single page by collectionType and slug ([76afb21](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/76afb21))
- **bg:**
  - Refactor background (plum, dot, particle) as custom elements ([30f377f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/30f377f))

### 🏡 Chore

- Init by create-astro ([a5a5ead](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/a5a5ead))
- Set up prettier for astro ([2e36460](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/2e36460))
- Set up eslint (flat) for ts, astro 1. init by 'npm init @eslint/config@latest' 2. disable formatting rules in eslint ([64115a0](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/64115a0))
- Lint staged ([e5000ef](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/e5000ef))
- Tsconfig ([4829f37](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/4829f37))
- Configure unocss. ([2b5e7b1](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/2b5e7b1))
- Change to npm & add .gitattributes ([8e722bb](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/8e722bb))
- Change to pnpm & update deps ([e19c95e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/e19c95e))
- Refactor configuration structure ([00ab263](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/00ab263))
- Update deps & pnpm format & pnpm lint ([bd4534f](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/bd4534f))
- Add workflows (ci, release, stale) & templates for PRs and issues ([bd6016c](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/bd6016c))
- Adjust views & update CI workflow & use 'vitepress' for callouts theme ([9f44e46](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/9f44e46))
- Organize CSS files & modify styles for headings under .prose ([52ed632](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/52ed632))
- Update homepage & README ([1fec5cf](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/1fec5cf))
- Update docs ([bade804](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/bade804))
- Temporary update (WIP) ([371bed3](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/371bed3))
- Update docs ([b78aca5](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/b78aca5))
- Add og images ([8c582c3](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/8c582c3))

### ❤️ Contributors

- Stephanie Lin ([@lin-stephanie](http://github.com/lin-stephanie))

[View changes on GitHub](https://github.com/lin-stephanie/astro-antfustyle-theme/compare/a5a5ead02fe5638c554639d078c85389a000ea5a...1.0.0)
