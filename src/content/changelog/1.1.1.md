---
title: '🩹 1.1.1: Background Animation Optimization'
description: Release Astro AntfuStyle Theme 1.1.1
pubDate: 2024-10-23
toc: true
share: true
ogImage: false
---

### 🐞 Bug Fixes

:::details
::summary[**bg:** Mitigate CPU spike caused by 'dot' background animation (fix [#1](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/1)) ([8fb85e1](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/8fb85e1))] 
- Lowered frame rate to reduce renders per second
- Optimized drawing method to decrease computation
- Reduced CPU usage by approximately 55% (from 70% to 31%)
:::

### 🏡 Chore

- Update docs ([64cb7c6](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/64cb7c6))
- Update dev/prod deps ([e7665b5](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/e7665b5))
- Update docs & adjust style import ([c20bace](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c20bace))

### ❤️ Contributors

- Stephanie Lin ([@lin-stephanie](http://github.com/lin-stephanie))

[View changes on GitHub](https://github.com/lin-stephanie/astro-antfustyle-theme/compare/1.1.0...1.1.1)
