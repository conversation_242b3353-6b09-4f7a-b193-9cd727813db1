---
title: '🗂️ 1.6.0: Share Your Short Notes or Quick Thoughts'
description: Release Astro AntfuStyle Theme 1.6.0
pubDate: 2025-03-31
toc: true
share: true
ogImage: false
---

### 🚨 Breaking Changes

- Install `remark-directive-sugar` while removing unneeded built-in plugins; update docs & deps ([615c41e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/615c41e))
  - Remark directives ([`::video`](https://astro-antfustyle-theme.vercel.app/blog/markdown-mdx-extended-features/#video-embeddingvideo), [`:link`](https://astro-antfustyle-theme.vercel.app/blog/markdown-mdx-extended-features/#styled-linklink), [`:badge`](https://astro-antfustyle-theme.vercel.app/blog/markdown-mdx-extended-features/#badgesbadge)) usage has changed

### 🚀 Features

- **page:** Add `/shorts` page to share short notes or quick thoughts ([#23](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/23))
- **nav:** Support `'hr'` in `navBarLayout` option for custom dividers ([76a7811](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/76a7811))
- **toc:** Support `'content'` in `displayPosition` option to show TOC on hover over content ('.prose') ([654473b](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/654473b))

### 🐞 Bug Fixes

- Hide default marker for Safari compatibility ([5a3f07b](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/5a3f07b))
- Update Toc.astro to support Chinese text anchor ([#21](https://github.com/lin-stephanie/astro-antfustyle-theme/pull/21))

### 🏡 Chore

- Refine `Warning` component ([0053c9e](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/0053c9e))
- Tweak styles & format code ([d23defe](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/d23defe))

### ❤️ Contributors

- Stephanie Lin ([@lin-stephanie](https://github.com/lin-stephanie))
- Summer ([@infinitesum](https://github.com/infinitesum))

[View changes on GitHub](https://github.com/lin-stephanie/astro-antfustyle-theme/compare/1.5.0...1.6.0)
