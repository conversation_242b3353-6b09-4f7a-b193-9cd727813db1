---
title: '📱 1.2.0: Supports Merging Links into a Menu on Mobile'
description: Release Astro AntfuStyle Theme 1.2.0
pubDate: 2024-11-02
toc: true
share: true
ogImage: false
---

### 🚀 Features

- :::details
  ::summary[**nav:** Add `mergeOnMobile` option to merge navigation and social links on mobile ([9e7cdcd](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/9e7cdcd))]
  - Added Backdrop for overlay when panel opens
  - Updated NavBar with NavItem and NavSwitch for mobile menu merge
  - Added fade effects for panel and overlay in main.css
  - Moved ToTopButton out of NavBar for better tab navigation
  - Extracted rss link as RssLink
  - Adjusted shadow for dark mode
  - Added `UI.navBarLayout.mergeOnMobile`
  :::
- :::details
  ::summary[**search:** Full-screen search panel with backdrop for viewport < 1128px ([f88318b](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f88318b))]
  - Use `display` for search panel visibility instead of `opacity`
  - Move `SearchSwitch` styles to main.css
  - Handle panel close on backdrop click in Backdrop
  - Handle panel close on 'Tab' focus outside the panel in Backdrop
  :::

### 🐞 Bug Fixes

- **style:** Ensure responsive alignment for social sections on `/` ([4b567c0](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/4b567c0))
- :::details
  ::summary[**rss:** Resolve MDX post parsing error ([c9ef3f2](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/c9ef3f2))]
  - Fixes an error due to missing `compiledContent()` method on item in MDX, which cannot be invoked
  - Omit generating the `content` field directly (as it includes unprocessed special syntax, making its generation unnecessary)
  :::

### 🏡 Chore

- Update docs, fix typo, update templates, adjust styles ([f42173b](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/f42173b))
- Add animation utilities, rename utils filenames, update deps ([3d480d0](https://github.com/lin-stephanie/astro-antfustyle-theme/commit/3d480d0))

### ❤️ Contributors

- Stephanie Lin ([@lin-stephanie](http://github.com/lin-stephanie))

[View changes on GitHub](https://github.com/lin-stephanie/astro-antfustyle-theme/compare/1.1.1...1.2.0)
