---
title: AstroEco is Releasing...
description: Display your GitHub releases using astro-loader-github-releases
subtitle: ''
bgType: plum
toc: false
ogImage: false
---

import BaseLayout from '~/layouts/BaseLayout.astro'
import StandardLayout from '~/layouts/StandardLayout.astro'
import GithubView from '~/components/views/GithubView.astro'
import Link from '~/components/base/Link.astro'

<BaseLayout
  title={frontmatter.title}
  description={frontmatter.description}
  bgType={frontmatter.bgType}
  ogImage={frontmatter.ogImage}
>
  <StandardLayout class="max-w-65ch" isCentered={true}>
    <Fragment slot="head">
      <h1>AstroEco is <span class="animate-pulse">Releasing...</span></h1>
      <p class="mt--4! op-50 italic"
      >Display your GitHub releases using <Link href="https://github.com/lin-stephanie/astro-loaders/tree/main/packages/astro-loader-github-releases" external={true} class="op-100!">astro-loader-github-releases</Link></p>
    </Fragment>
    <GithubView collectionType="releases" />
  </StandardLayout>
</BaseLayout>
