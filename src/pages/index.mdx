---
title: 'BreezeWhisper的博客'
subtitle: ''
description: ''
bgType: plum
toc: false
ogImage: false
---

import BaseLayout from '~/layouts/BaseLayout.astro'
import StandardLayout from '~/layouts/StandardLayout.astro'
import RenderPage from '~/components/views/RenderPage.astro'

<BaseLayout
  title={frontmatter.title}
  description={frontmatter.description}
  bgType={frontmatter.bgType}
  ogImage={frontmatter.ogImage}
>
  <StandardLayout title={frontmatter.title} subtitle={frontmatter.subtitle}>
    <RenderPage
      slot="article"
      collectionType="home"
      id="index"
      pageToc={frontmatter.toc}
    />
  </StandardLayout>
</BaseLayout>
