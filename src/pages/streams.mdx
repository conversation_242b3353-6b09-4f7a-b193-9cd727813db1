---
title: Astro Streams
description: 'Example: displaying Astro streams with local JSON data'
subtitle: 'Example: displaying Astro streams with local JSON data'
bgType: false
toc: false
ogImage: false
---

import BaseLayout from '~/layouts/BaseLayout.astro'
import TabbedLayout from '~/layouts/TabbedLayout.astro'
import ListView from '~/components/views/ListView.astro'

<BaseLayout
  title={frontmatter.title}
  description={frontmatter.description}
  bgType={frontmatter.bgType}
  ogImage={frontmatter.ogImage}
>
  <TabbedLayout subtitle={frontmatter.subtitle}>
    <ListView collectionType="streams" pageToc={frontmatter.toc} />
  </TabbedLayout>
</BaseLayout>
