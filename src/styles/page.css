/* /releases & /prs */
.github-item-prose h1,
.github-item-prose h2,
.github-item-prose h3,
.github-item-prose h4,
.github-item-prose h5 {
  --uno: 'text-1em lt-sm:text-sm';
}

.github-item-prose > :first-child {
  margin-top: 1.6em;
}

.github-item-prose > :last-child {
  margin-bottom: 1.25em;
}

.github-item-prose h5 a {
  font-size: 0.875em;
}

.github-item-prose li {
  --uno: 'lt-sm:text-sm';
}

summary::-webkit-details-marker {
  display: none;
}

/* /highlights */
.card-item-prose p {
  margin-bottom: 1.1em;
  margin-top: 1.1em;
}

.card-masonry > *:first-child {
  border-radius: 0.5rem;

  --uno: 'hover:(shadow-custom_0_3_20_-5)';
}

/* /shorts */
.card-grid > *:first-child {
  border-radius: 0.75rem;

  --uno: 'hover:(shadow-custom_0_0_15_-3 scale-103 transition-transform)';
}

/* /photos */
.photo-view[data-layout='masonry'] .icon-masonry {
  display: block;
}

.photo-view[data-layout='square'] .icon-grid {
  display: block;
}

.photo-view[data-layout='square'] .photo-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--min-photo-width), 1fr));
  gap: var(--photo-gap);
}

.photo-figure {
  cursor: pointer;
  overflow: hidden;

  background-size: cover;
  background-image: var(--photo-placeholder);
}

.photo-view[data-layout='masonry'] .photo-figure {
  position: absolute;
  top: 0;
  left: 0;

  width: var(--photo-width);
  height: var(--photo-height);
  border-radius: 0.375rem;

  transform: translate(var(--photo-left), var(--photo-top));
}

.photo-view[data-layout='square'] .photo-figure {
  position: relative;
  aspect-ratio: 1;
}

.photo-figure img {
  width: 100%;
  height: 100%;

  object-fit: cover;
  opacity: var(--photo-opacity, 0);
  transition: all 0.3s ease-in-out;
}

.photo-figure:hover img,
.photo-figure:focus img {
  transform: scale(1.05);
}

.photo-figure figcaption {
  position: absolute;
  bottom: 0;
  left: 0;

  width: 100%;
  padding: 1rem 0.6rem 0.6rem;

  color: white;
  font-size: 0.875rem;

  opacity: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);

  transition: all 0.3s ease-in-out;
}

.photo-figure:hover figcaption,
.photo-figure:focus figcaption {
  opacity: 1;
}
