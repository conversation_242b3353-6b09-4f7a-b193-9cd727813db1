:root {
  --c-bg: #fff;
  --c-scrollbar: #ddd;
  --c-scrollbar-hover: #bbb;
  --c-text: #000;
}

:root.dark {
  --c-bg: #050505;
  --c-scrollbar: #222;
  --c-scrollbar-hover: #444;
  --c-text: #fff;
}

html {
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  overflow-y: scroll;

  background-color: var(--c-bg);
}

::selection {
  background: #8884;
}

/* NProgress */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  z-index: 50;
  position: fixed;
  top: 0;
  left: 0;

  width: 100%;
  height: 2px;

  background: #888;
  opacity: 0.75;
}

/* Scrollbar */
@supports (scrollbar-width: auto) {
  * {
    scrollbar-width: auto;
    scrollbar-color: var(--c-scrollbar) var(--c-bg);
  }

  #search-results,
  #nav-panel,
  #toc-panel {
    scrollbar-width: none;
  }
}

@supports selector(::-webkit-scrollbar) {
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar:horizontal {
    height: 8px;
  }

  ::-webkit-scrollbar-track,
  ::-webkit-scrollbar-corner {
    border-radius: 10px;
    background: var(--c-bg);
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: var(--c-scrollbar);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--c-scrollbar-hover);
  }

  .table-of-contents > ul::-webkit-scrollbar,
  #search-results::-webkit-scrollbar {
    width: 0;
  }

  .table-of-contents > ul::-webkit-scrollbar:horizontal,
  #search-results::-webkit-scrollbar:horizontal {
    height: 0;
  }
}

/* Slide Enter Animation */
@keyframes slide-enter {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (prefers-reduced-motion: no-preference) {
  body:not([data-no-sliding]) .slide-enter,
  body:not([data-no-sliding]) .slide-enter-content > *:not(aside) {
    --enter-initial: 0ms;
    --enter-stage: 0;
    animation: slide-enter 1s both 1;
    animation-delay: calc(
      var(--enter-initial) + var(--enter-step) * var(--enter-stage)
    );
  }

  .slide-enter-content > *:nth-child(1) {
    --enter-stage: 1 !important;
  }
  .slide-enter-content > *:nth-child(2) {
    --enter-stage: 2 !important;
  }
  .slide-enter-content > *:nth-child(3) {
    --enter-stage: 3 !important;
  }
  .slide-enter-content > *:nth-child(4) {
    --enter-stage: 4 !important;
  }
  .slide-enter-content > *:nth-child(5) {
    --enter-stage: 5 !important;
  }
  .slide-enter-content > *:nth-child(6) {
    --enter-stage: 6 !important;
  }
  .slide-enter-content > *:nth-child(7) {
    --enter-stage: 7 !important;
  }
  .slide-enter-content > *:nth-child(8) {
    --enter-stage: 8 !important;
  }
  .slide-enter-content > *:nth-child(9) {
    --enter-stage: 9 !important;
  }
  .slide-enter-content > *:nth-child(10) {
    --enter-stage: 10 !important;
  }
  .slide-enter-content > *:nth-child(11) {
    --enter-stage: 11 !important;
  }
  .slide-enter-content > *:nth-child(12) {
    --enter-stage: 12 !important;
  }
  .slide-enter-content > *:nth-child(13) {
    --enter-stage: 13 !important;
  }
  .slide-enter-content > *:nth-child(14) {
    --enter-stage: 14 !important;
  }
  .slide-enter-content > *:nth-child(15) {
    --enter-stage: 15 !important;
  }
  .slide-enter-content > *:nth-child(16) {
    --enter-stage: 16 !important;
  }
  .slide-enter-content > *:nth-child(17) {
    --enter-stage: 17 !important;
  }
  .slide-enter-content > *:nth-child(18) {
    --enter-stage: 18 !important;
  }
  .slide-enter-content > *:nth-child(19) {
    --enter-stage: 19 !important;
  }
  .slide-enter-content > *:nth-child(20) {
    --enter-stage: 20 !important;
  }
}

/* View Transition API */
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  z-index: 1;
}

::view-transition-new(root) {
  z-index: 9999;
}

.dark::view-transition-old(root) {
  z-index: 9999;
}

.dark::view-transition-new(root) {
  z-index: 1;
}

/* Panel Animation */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.fade-in {
  animation: fade-in var(--fade-in-duration, 0.3s) forwards;
}

.fade-out {
  animation: fade-out var(--fade-out-duration, 0.3s) forwards;
}

/* Search */
#search-bar:hover #search-icon,
#search-bar:focus-within #search-icon {
  opacity: 1;
}

input[type='search']::-webkit-search-cancel-button {
  display: none;
}

.search-results-item:first-child {
  margin-top: 8px;
}

.search-results-item {
  display: flex;
  flex-direction: column;
  gap: 1px;

  padding: 0.75rem;
  border-radius: 6px;

  opacity: 0.6;
  background: transparent;

  font-size: 1.1rem;
  transition: opacity 0.2s ease;
}

.search-results-item:hover {
  opacity: 1;
  --uno: 'bg-[#88888811] dark:bg-[#88888822]';
}

.search-results-title {
  color: var(--c-text);
}

.search-results-excerpt {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;

  opacity: 0.5;
}

.search-results-excerpt mark {
  background: transparent;

  color: var(--c-text);
  font-size: 1.06em;
}
