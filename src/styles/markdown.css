@import 'viewerjs/dist/viewer.css';
@import 'rehype-callouts/theme/vitepress';
@import url('https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css');

/* Prose Complement */
.prose {
  --fg-light: #888;
  --fg: #555;
  --fg-deep: #222;
  --fg-deeper: var(--c-text);

  color: var(--fg);
}

.prose a {
  border-bottom: 1px solid rgba(125, 125, 125, 0.3);
  transition: border 0.3s ease-in-out;
}

.prose a:hover {
  border-bottom: 1px solid var(--fg);
}

.prose a code {
  color: inherit;
}

.prose hr {
  width: 50px;
  margin: 2em auto;
}

html.dark .prose {
  --fg-light: #888;
  --fg: #bbb;
  --fg-deep: #ddd;
  --fg-deeper: var(--c-text);
}

.prose blockquote {
  font-weight: normal;
  font-style: normal;
  line-height: 1.6em;
  padding: 0.5em 1em;
  margin-left: -1.1em;
}

.prose blockquote > * {
  opacity: 0.7;
}

.prose blockquote > :first-child {
  margin-top: 0;
}

.prose blockquote > :last-child {
  margin-bottom: 0;
}

.prose blockquote p:first-of-type::before {
  content: none;
}

.prose blockquote p:first-of-type::after {
  content: none;
}

.prose s {
  opacity: 0.5;
}

.prose em {
  --uno: 'font-serif';
  color: var(--fg-deep);
  font-size: 1.05em;
}

.prose details summary {
  padding-left: 1px;
  cursor: pointer;
}

.prose details:not(.expressive-code *) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose li details {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose thead th,
.prose tbody td:first-child {
  white-space: nowrap;
}

.prose code:not(.expressive-code code) {
  border-radius: 0.25rem;
  padding: 0.15rem 0.3rem;
  --uno: 'bg-[#88888820] dark:bg-[#88888830]';
}

/* Toc */
.toc-desktop {
  position: fixed;
  top: 8.875rem;
  bottom: 0;
  left: 2.05rem;

  z-index: 100;
  display: flex;
  flex-direction: column;
  --uno: 'lt-lgp:hidden';
  overflow: hidden;

  width: 15.625rem;
  max-height: 74vh;

  font-size: 0.875em;
}

.toc-desktop-anchor {
  display: flex;
  align-items: center;

  font-size: 1rem;
  opacity: 0.3;
  transition: all 700ms;
}

.toc-desktop-on > .toc-desktop-anchor,
.prose:hover .toc-desktop-content > .toc-desktop-anchor,
.toc-desktop:hover > .toc-desktop-anchor {
  opacity: 0.6;
}

.toc-desktop > ul {
  overflow-y: auto;

  height: 100%;
  margin-top: 0.25rem;

  opacity: 0;
  transition: all 700ms;
}

.toc-desktop-on > ul,
.prose:hover .toc-desktop-content > ul,
.toc-desktop:hover > ul {
  opacity: 0.6;
}

.toc-desktop ul > li::before,
#toc-panel ul > li::before {
  display: none;
}

.toc-desktop ul > li,
#toc-panel ul > li {
  padding-left: 0.8rem;
  margin-top: 0.5em;

  line-height: 1.6em;
}

.toc-desktop > ul > li,
#toc-panel > ul > li {
  padding-left: 0rem;
}

.toc-desktop ul > li > a,
#toc-panel ul > li > a {
  display: block;
  overflow-x: hidden;

  border-bottom: 0px;

  color: var(--c-text);
  text-overflow: ellipsis;
  white-space: nowrap;

  opacity: 0.75;

  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.toc-desktop ul > li > a:hover,
#toc-panel ul > li > a:hover {
  border-bottom: 0px;
}

.toc-desktop-right.toc-desktop {
  left: auto;
  right: 0rem;
}

#toc-panel ul {
  margin-top: 0;
  margin-bottom: 0;
}

#toc-panel ul > li > a {
  color: inherit;
  opacity: 0.6;
}

.toc-desktop [aria-current='true'],
#toc-panel [aria-current='true'] {
  opacity: 1;
}

/* Expressive Code */
.expressive-code {
  margin: 1em 0;
}

.expressive-code .copy button::before {
  display: none;
}

.expressive-code .copy button {
  width: 2rem !important;
  height: 2rem !important;
}

.expressive-code .copy button::after {
  width: 1rem !important;
  height: 1rem !important;
  opacity: 0.5 !important;
}

.expressive-code .copy button:hover::after,
.expressive-code .copy button:active::after {
  opacity: 1 !important;
}

.expressive-code .copy .feedback {
  font-size: 14px;
  border-width: 0;
}

.expressive-code .ec-section summary .code {
  display: flex;
  align-items: center;
}

.expressive-code .ec-section summary svg {
  display: inline-block;
}

.expressive-code .gutter .ln {
  padding-inline: 1ch !important;
}

.expressive-code .collapsible-start .code {
  height: 1.4em;
}

/* viewerjs */
.viewer-canvas.viewer-loading > img {
  opacity: 0;
}

.viewer-backdrop {
  background-color: rgba(0, 0, 0, 0.9);
}

.viewer-navbar {
  margin-bottom: 5px;
  background-color: transparent;
}

.viewer-list > li {
  margin-right: -1px;
  border-radius: 1px;
  border: 1px solid #000;
}

article img:not(.no-zoom) {
  cursor: zoom-in;
}

/* rehype-autolink-headings */
a.header-anchor {
  float: left;
  margin-left: -0.9em;
  border: 0 !important;
  opacity: 0;
  font-size: 1em;
  text-decoration: none;
}

a.header-anchor:hover,
a.header-anchor:focus {
  text-decoration: none;
}

h1:hover .header-anchor,
h1:focus .header-anchor,
h2:hover .header-anchor,
h2:focus .header-anchor,
h3:hover .header-anchor,
h3:focus .header-anchor,
h4:hover .header-anchor,
h4:focus .header-anchor,
h5:hover .header-anchor,
h5:focus .header-anchor,
h6:hover .header-anchor,
h6:focus .header-anchor {
  opacity: 0.5;
}

/* rehype-callouts */
.callout {
  padding-left: 16px;
  font-size: 15px;
}

.callout p {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.callout-title-icon {
  padding-right: 2px;
}

/* :::iamge */
.dark .img-light {
  display: none;
}

html:not(.dark) .img-dark {
  display: none;
}

/* ::vedio */
.rds-video {
  z-index: 150;
  position: relative;

  width: 100%;
  aspect-ratio: 16 / 9;
  border-radius: 0.5rem;
  margin: 5rem 0;

  transform: scale(1.2, 1.2) !important;

  --uno: 'lt-lg:(mx-4 scale-0)';
}

.rds-video.no-scale {
  margin: 1rem 0;
  transform: scale(1, 1) !important;
}

/* :link */
[data-link='github-acct'],
[data-link='custom-url'].rounded {
  display: inline-flex;
  align-items: center;
  padding-right: 0.5rem;
  border-radius: 9999px;
  border-width: 0px !important;

  --uno: 'font-condensed';
  color: var(--fg-light) !important;
  font-size: 0.875rem;
  line-height: 1.25rem;

  background: #8882;
  transform: translateY(6px);
  transition: all 300ms ease-in-out !important;
}

[data-link='github-acct'] > img,
[data-link='custom-url'].rounded > img {
  display: inline-block;
  height: 1.6em;
  width: 1.6em;
  border-radius: 50%;
  margin: 0 0.25rem 0 0;

  object-fit: cover;
}

[data-link='github-repo']:not(.github),
[data-link='custom-url'].square {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.375rem;
  border-radius: 0.25rem;
  border-width: 0px !important;

  --uno: 'font-condensed';
  color: var(--fg-light) !important;
  line-height: 100%;

  background: #8882;
  transform: translateY(3px);
  transition: all 300ms ease-in-out !important;
}

[data-link='github-repo']:not(.github) > img,
[data-link='custom-url'].square > img {
  display: inline-block;
  height: 1.1em;
  width: 1.1em;
  border-radius: 2px;
  margin: 0 0.25rem 0 0;

  object-fit: cover;
}

[data-link='github-acct']:hover,
[data-link='github-repo']:not(.github):hover,
[data-link='custom-url'].rounded:hover,
[data-link='custom-url'].square:hover {
  color: var(--fg) !important;

  background: #8883;
}

[data-link='github-repo'].github,
[data-link='npm-pkg'] {
  --uno: 'font-mono';
}

[data-link='custom-url'] > img,
[data-link='github-repo'].github > img,
[data-link='npm-pkg'] > img {
  display: inline-block;
  width: 1.05em;
  height: 1.05em;
  border-radius: 0;
  margin: 0 0.25rem 0.2rem 0.1rem;
}

/* :badge */
.rds-badge {
  display: inline-block;

  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  margin-right: 0.5rem;

  color: white;
  font-size: 0.75rem;
  line-height: 1rem;
  text-transform: uppercase;

  background-color: var(--badge-color-light, #a8a29e);
}

.dark .rds-badge {
  color: black;
  background-color: var(--badge-color-dark, #a8a29e);
}

[data-badge='n'] {
  --badge-color-light: #f87171;
  --badge-color-dark: #fecaca;
}

[data-badge='a'] {
  --badge-color-light: #fb923c;
  --badge-color-dark: #fed7aa;
}

[data-badge='v'] {
  --badge-color-light: #facc15;
  --badge-color-dark: #fef08a;
}

/* rehype-external-links */
.new-tab-icon {
  margin-left: 0.1rem;
  margin-bottom: 0.3rem;

  font-size: 0.6em;
}

.external-link-cursor {
  cursor: var(--external-link-cursor);
}
