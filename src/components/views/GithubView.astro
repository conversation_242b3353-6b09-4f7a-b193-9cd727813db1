---
import { getCollection } from 'astro:content'

import Link from '~/components/base/Link.astro'
import GithubItem from '~/components/views/GithubItem.astro'
import Warning from '~/components/base/Warning.astro'
import { UI } from '~/config'
import { formatDate, isDiffMonth } from '~/utils/datetime'
import { withBasePath } from '~/utils/path'
import { matchLogo, extractPackageName, processVersion } from '~/utils/data'

import type { RepoWithOwner, Url, Icon } from '~/types'
import type { CollectionEntry } from 'astro:content'

export type ExtendedReleaseData = CollectionEntry<'releases'>['data'] & {
  isInMonorepo: boolean
  pkgName: string
  mainLogo: Url | Icon
  subLogo: Url | Icon | undefined
  versionType: 'major' | 'minor' | 'patch' | 'pre'
  versionParts: [string, string]
}

export type ExtendedPrData = CollectionEntry<'prs'>['data'] & {
  mainLogo: Url | Icon
  subLogo: Url | Icon | undefined
}

interface Props {
  collectionType: 'releases' | 'prs'
}

const { collectionType } = Astro.props
const { monorepos, mainLogoOverrides, subLogoMatches } = UI.githubView

const MAX_PER_PKG = 5
const WARNING = `No GitHub data available for display.
  See <a href='https://astro-antfustyle-theme.vercel.app/blog/customizing-github-activity-pages/'>
  Customizing GitHub Activity Pages</a> for details.`

let isEmpty = false

/* Releases */
let releases: CollectionEntry<'releases'>[] = []
let extendedReleases: ExtendedReleaseData[] = []

if (collectionType === 'releases') {
  releases = await getCollection(collectionType)

  const pkgCountMap: Record<string, number> = {}
  extendedReleases = releases
    .map((item) => {
      const isInMonorepo = monorepos.includes(
        item.data.repoNameWithOwner as RepoWithOwner
      )
      const pkgName = isInMonorepo
        ? extractPackageName(item.data.tagName)
        : item.data.repoName

      const mainLogo =
        matchLogo(pkgName, mainLogoOverrides) ||
        `https://github.com/${item.data.repoOwner}.png`
      const subLogo = matchLogo(pkgName, subLogoMatches)

      const [versionType, ...versionParts] = processVersion(
        item.data.versionNum
      )

      return {
        ...item.data,
        isInMonorepo,
        pkgName,
        mainLogo,
        subLogo,
        versionType,
        versionParts,
      }
    })
    // filters releases to ensure each pkg has no more than a specified maximum number
    .filter((item) => {
      if ((pkgCountMap[item.pkgName] || 0) < MAX_PER_PKG) {
        pkgCountMap[item.pkgName] = (pkgCountMap[item.pkgName] || 0) + 1
        return true
      }
      return false
    })
    .sort((a, b) => {
      return +new Date(b.publishedAt) - +new Date(a.publishedAt)
    })

  isEmpty = releases.length === 0 || extendedReleases.length === 0
}

/* PRs */
let prs: CollectionEntry<'prs'>[] = []
let extendedPrs: ExtendedPrData[] = []

if (collectionType === 'prs') {
  prs = await getCollection(collectionType)

  extendedPrs = prs
    .filter(
      (item) =>
        item.data.state !== 'CLOSED' &&
        !item.data.isDraft &&
        !/\b(?:chore|docs|i18n)\b/.test(item.data.title)
    )
    .map((item) => {
      const mainLogo =
        matchLogo(item.data.repository.name, mainLogoOverrides) ||
        `https://github.com/${item.data.repository.owner.login}.png`
      const subLogo = matchLogo(item.data.repository.name, subLogoMatches)

      return {
        ...item.data,
        mainLogo,
        subLogo,
      }
    })
    .sort((a, b) => {
      return +new Date(b.createdAt) - +new Date(a.createdAt)
    })

  isEmpty = prs.length === 0 || extendedPrs.length === 0
}
---

{
  isEmpty ? (
    <Warning html={WARNING} />
  ) : (
    <div class="flex flex-col gap-8 lt-sm:gap-6 mt-16.8">
      {collectionType === 'releases' &&
        extendedReleases.map((item, idx) => (
          <GithubItem
            collectionType={collectionType}
            idx={idx}
            item={item}
            isDiffMonth={isDiffMonth(
              item.publishedAt,
              extendedReleases[idx - 1]
                ? extendedReleases[idx - 1].publishedAt
                : undefined
            )}
          />
        ))}
      {collectionType === 'prs' &&
        extendedPrs.map((item, idx) => (
          <GithubItem
            collectionType={collectionType}
            idx={idx}
            item={item}
            isDiffMonth={isDiffMonth(
              item.createdAt,
              extendedPrs[idx - 1] ? extendedPrs[idx - 1].createdAt : undefined
            )}
          />
        ))}

      <div class="prose flex flex-col items-center mx-a op-50 text-center text-3.75! lt-sm:text-sm!">
        <hr class="mt-3em!" />
        <p class="flex lt-sm:flex-col items-center my-0! lt-sm:my-1.5!">
          <span>
            Last fetched:
            <time
              class="inline-block text-[var(--fg-deeper)]"
              datetime={new Date().toISOString()}
              title={new Date().toISOString()}
            >
              {formatDate(new Date())}
            </time>
          </span>
          <span class="lt-sm:hidden">&nbsp;|&nbsp;</span>
          <span>
            Scheduled refresh:
            <span class="text-[var(--fg-deeper)]">Every Saturday</span>
          </span>
        </p>
        <p class="my-0! lt-sm:my-1.5!">
          See
          <Link
            class="op-100!"
            href={withBasePath('/blog/customizing-github-activity-pages/')}
          >
            Customizing GitHub Activity Pages
          </Link>
          to configure your own
        </p>
        <p class="my-0! lt-sm:my-1.5!">
          Inspired by
          <Link
            class="op-100!"
            href={
              collectionType === 'releases'
                ? 'https://releases.antfu.me/'
                : 'https://prs.atinux.com/'
            }
          >
            {collectionType === 'releases'
              ? 'releases.antfu.me'
              : 'prs.atinux.com'}
          </Link>
        </p>
      </div>
    </div>
  )
}
