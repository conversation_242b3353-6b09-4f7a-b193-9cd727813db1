---
import { render } from 'astro:content'

import BaseLayout from '~/layouts/BaseLayout.astro'
import StandardLayout from '~/layouts/StandardLayout.astro'
import PostMeta from '~/components/base/PostMeta.astro'
import Warning from '~/components/base/Warning.astro'
import Toc from '~/components/toc/Toc.astro'
import ShareLink from '~/components/widgets/ShareLink.astro'
import Giscus from '~/components/widgets/Giscus.astro'
import ImageViewer from '~/components/widgets/ImageViewer.astro'
import { FEATURES } from '~/config'

import type { CollectionEntry } from 'astro:content'

interface Props {
  post: CollectionEntry<'blog' | 'changelog'>
  isSearchable?: boolean
  isCommentable?: boolean
}

const { post, isSearchable = false, isCommentable = false } = Astro.props
const { toc, share, giscus } = FEATURES

const { data: frontmatter } = post
const { Content, headings, remarkPluginFrontmatter } = await render(post)

const title = frontmatter.title
const description = frontmatter.description
const ogImage = frontmatter.ogImage
const pubDate = frontmatter.pubDate.toISOString()
const lastModDate =
  frontmatter.lastModDate && frontmatter.lastModDate.toISOString()
const subtitle = frontmatter.subtitle
const minutesRead =
  frontmatter.minutesRead || (remarkPluginFrontmatter.minutesRead as number)
const isDraft = frontmatter.draft
const tocEnabled = Array.isArray(toc) && toc[0] && frontmatter.toc
const shareEnabled = Array.isArray(share) && share[0] && frontmatter.share
const giscusEnabled =
  isCommentable && Array.isArray(giscus) && giscus[0] && frontmatter.giscus

const WARNING = `This is a draft post that will not be published
in production. Set the post's <code>draft</code> property in
frontmatter to <code>false</code> or remove it to publish the post.`
---

<BaseLayout {title} {description} {ogImage} {pubDate} {lastModDate}>
  <StandardLayout {title} {subtitle} {isSearchable}>
    <Fragment slot="head">
      <PostMeta {pubDate} {lastModDate} {minutesRead} />
    </Fragment>
    <Fragment slot="article">
      {isDraft && <Warning html={WARNING} />}
      {tocEnabled && <Toc {headings} />}
      <Content />
    </Fragment>
  </StandardLayout>
  <Fragment slot="share">
    {shareEnabled && <ShareLink config={share[1]} />}
  </Fragment>
  {
    giscusEnabled && (
      <div slot="giscus" class="prose mx-auto mt-8">
        <hr />
        <Giscus />
      </div>
    )
  }
  <ImageViewer selector="article" options={{ title: 0, navbar: 0 }} />
</BaseLayout>
