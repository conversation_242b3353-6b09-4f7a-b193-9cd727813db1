---
import { getEntry, render } from 'astro:content'

import Toc from '~/components/toc/Toc.astro'
import { FEATURES } from '~/config'

import type { CollectionKey } from 'astro:content'

interface Props {
  collectionType: CollectionKey
  id: string
  pageToc?: boolean
}

const { collectionType, id, pageToc = false } = Astro.props

/* Page */
const content = await getEntry(collectionType, id)
if (!content) throw new Error(`Entry not found: ${collectionType}/${id}`)
const { Content, headings } = await render(content)

/* Toc */
const { toc } = FEATURES
const tocEnabled = Array.isArray(toc) && toc[0] && pageToc
---

{tocEnabled && <Toc {headings} />}
<Content />
