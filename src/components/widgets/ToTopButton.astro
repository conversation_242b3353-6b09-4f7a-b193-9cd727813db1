---

---

<button
  id="to-top-button"
  class="z-50 fixed right-5 bottom-6
    w-10 h-10 rounded-full
    op-0 pointer-events-none op-transition
    lt-lgp:(op-50! bg-#8883 dark:bg-#8886 pointer-events-auto!)
    hover:(op-100! bg-#8883) print:hidden"
  title="Scroll to top"
>
  <div u-i-ri-arrow-up-line></div>
</button>

<script>
  document.addEventListener('astro:page-load', () => {
    const handleClick = () => {
      window.scrollTo({ top: 0 })
    }
    const toTopButton = document.getElementById('to-top-button')
    toTopButton?.addEventListener('click', handleClick)
  })

  const handleScroll = () => {
    const scrollThreshold = 300
    const toTopButton = document.getElementById('to-top-button')

    if (toTopButton && window.matchMedia('(min-width: 1024px)').matches) {
      const scrollY = window.scrollY || document.documentElement.scrollTop
      if (scrollY <= scrollThreshold) {
        toTopButton.style.opacity = '0'
        toTopButton.style.pointerEvents = 'none'
      } else {
        toTopButton.style.opacity = '0.3'
        toTopButton.style.pointerEvents = 'auto'
      }
    }
  }
  window.addEventListener('scroll', handleScroll)
</script>
