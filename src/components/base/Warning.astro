---
interface Props {
  html: string
}

const { html } = Astro.props
---

<div class="prose mx-auto">
  <div class="callout" data-callout="warning" data-collapsible="false">
    <div class="callout-title">
      <div class="callout-title-icon" aria-hidden="true">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="1em"
          height="1em"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <g>
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 16v-4m0-4h.01"></path>
          </g>
        </svg>
      </div><div class="callout-title-text">WARNING</div>
    </div><div class="callout-content">
      <p set:html={html} />
    </div>
  </div>
</div>
