---

---

<div
  id="backdrop"
  class="hidden z-75 fixed inset-0
    backdrop-blur-2 bg-[#000000cc]"
  aria-hidden="true"
>
</div>

<script>
  import { toggleFadeEffect } from '~/utils/animation'

  const PANELS = [
    { id: 'nav-panel', button: 'nav-open-button', hiddenClass: 'hidden!' },
    { id: 'toc-panel', button: 'toc-open-button', hiddenClass: 'hidden!' },
    { id: 'search-panel', button: 'search-switch', hiddenClass: 'hidden' },
  ]

  /* Close panel when user click on backdrop */
  document.addEventListener('astro:page-load', () => {
    const handleClick = () => {
      for (const { id, hiddenClass } of PANELS) {
        const panel = document.getElementById(id)
        if (panel && !panel.classList.contains(hiddenClass)) {
          toggleFadeEffect(id, false, hiddenClass)
          break
        }
      }
      toggleFadeEffect('backdrop', false, 'hidden')
    }
    const backdrop = document.getElementById('backdrop')
    backdrop?.addEventListener('click', handleClick)
  })

  /* Close panel and backdrop when 'Tab' shifts focus outside the panel
    or 'Escape' is pressed, if backdrop is present */
  const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key !== 'Tab' && event.key !== 'Escape') return

    const focusEl = document.activeElement
    PANELS.forEach(({ id, button, hiddenClass }) => {
      const panel = document.getElementById(id)
      const isFocusOutside = !panel?.contains(focusEl) && focusEl?.id !== button

      if (
        panel &&
        !panel.classList.contains(hiddenClass) &&
        (isFocusOutside || event.key === 'Escape')
      ) {
        toggleFadeEffect(id, false, hiddenClass)
        toggleFadeEffect('backdrop', false, 'hidden')
        if (panel.contains(focusEl)) {
          document.getElementById(button)?.focus()
        }
      }
    })
  }
  document.addEventListener('keyup', handleKeyUp)
</script>
