---
import { slug } from '~/utils/toc'
import { UI } from '~/config'

interface Props {
  idx?: number
  text: string
  needId?: boolean
  wide?: boolean
}

const { idx, text, needId = false, wide = false } = Astro.props
const { maxGroupColumns } = UI.groupView
---

<div
  {...needId ? { id: slug(text) } : {}}
  class={`toc-heading relative h-20
    pointer-events-none select-none
    ${idx !== undefined ? 'slide-enter' : ''}`}
  style={idx !== undefined ? { '--enter-stage': idx - 2 } : undefined}
>
  {
    !wide ? (
      <span
        class="absolute left--12 top--8
          op-10 text-8em text-stroke-2 text-stroke-hex-aaa font-bold color-transparent"
      >
        {text}
      </span>
    ) : (
      <span
        class={`absolute top-0 op-35 dark:op-20
          text-5em lt-lg:text-4.5em lt-sm:(text-3.6em top-2) text-stroke-1.5 text-stroke-#aaa
          font-bold color-transparent leading-1em
          ${maxGroupColumns === 3 ? 'left--4' : 'left-[14%] lt-lg:left--4'}`}
      >
        {text}
      </span>
    )
  }
</div>
