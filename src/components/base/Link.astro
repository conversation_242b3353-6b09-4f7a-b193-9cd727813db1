---
import { UI } from '~/config'
import type { HTMLAttributes } from 'astro/types'

interface Props extends HTMLAttributes<'a'> {
  href?: string
  title?: string
  rel?: string
  external?: boolean
  enableNewTabWarning?: boolean
  children: HTMLElement | HTMLElement[] | string
}

const {
  href,
  title,
  rel,
  external = false,
  enableNewTabWarning = false,
  class: className,
  ...rest
} = Astro.props

const { externalLink } = UI

const openInNewTab = external && externalLink.newTab
const targetAttr = openInNewTab ? '_blank' : undefined
const relAttr =
  rel || openInNewTab
    ? `${openInNewTab ? 'noopener noreferrer' : ''} ${rel ? rel : ''}`.trim()
    : undefined
const ariaLabel = title ? title : openInNewTab ? 'Open in new tab' : undefined

const showCursor =
  openInNewTab &&
  enableNewTabWarning &&
  externalLink.cursorType &&
  externalLink.cursorType !== 'pointer'

const showIcon =
  openInNewTab && enableNewTabWarning && externalLink.showNewTabIcon
---

<a
  class:list={[
    'op-60 no-underline hover:op-100',
    { 'external-link-cursor': showCursor },
    className,
  ]}
  {href}
  {title}
  aria-label={ariaLabel}
  rel={relAttr}
  target={targetAttr}
  {...rest}
>
  {
    Astro.slots.has('default') && (
      <>
        <slot />{showIcon && (
          <span
            u-i-carbon-arrow-up-right
            class="new-tab-icon ml-0!"
            aria-hidden="true"
          />
        )}
      </>
    )
  }{
    Astro.slots.has('title') && (
      <span class="text-lg leading-1.3em align-middle">
        <slot name="title" />
        {showIcon && (
          <span
            u-i-carbon-arrow-up-right
            class="new-tab-icon ml--1! mb-1.8!"
            aria-hidden="true"
          />
        )}
      </span>
    )
  }<slot name="end" /></a
>
