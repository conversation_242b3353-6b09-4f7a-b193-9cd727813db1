---
import { <PERSON><PERSON>Router } from 'astro:transitions'
import { basename } from 'node:path'

import { checkFileExistsInDir } from '../../../plugins/remark-generate-og-image'
import { withBasePath } from '~/utils/path'
import { SITE } from '~/config'

interface Props {
  title?: string
  description?: string
  ogImage?: string | boolean
  pubDate?: string
  lastModDate?: string
}

const { title, description, ogImage, pubDate, lastModDate } = Astro.props

const siteName = new URL(SITE.website).hostname

const pageTitle =
  title && title !== SITE.title ? `${title} - ${SITE.title}` : SITE.title

const pageDescription = description || SITE.description

const canonicalURL = new URL(Astro.url.pathname, Astro.site)

const generatedOgImage = checkFileExistsInDir(
  'public/og-images',
  `${basename(Astro.url.pathname)}.png`
)
  ? withBasePath('og-images', `${basename(Astro.url.pathname)}.png`)
  : undefined

const assignedOgImage =
  ogImage &&
  ogImage !== true &&
  checkFileExistsInDir('public/og-images', ogImage)
    ? withBasePath('og-images', ogImage)
    : undefined

const ogImageURL = new URL(
  assignedOgImage ||
    generatedOgImage ||
    withBasePath('og-images', 'og-image.png'),
  Astro.url
).href

// https://jsonld.com/blog-post/
const blogPostingData = {
  '@context': 'https://schema.org',
  '@type': 'BlogPosting',
  'headline': pageTitle,
  'image': ogImageURL,
  'url': canonicalURL,
  'datePublished': pubDate,
  ...(lastModDate && { dateModified: lastModDate }),
  ...(description && { description: description }),
  'author': {
    '@type': 'Person',
    'name': SITE.author,
    'url': SITE.website,
  },
}

// https://jsonld.com/web-page/
const webPageData = {
  '@context': 'https://schema.org',
  '@type': 'webPage',
  'name': `${title || SITE.title}`,
  ...(description && { description: description }),
  'publisher': {
    '@type': 'ProfilePage',
    'name': `${SITE.author}'s Personal Website`,
  },
}
---

<!-- Global Metadata -->
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta name="generator" content={Astro.generator} />

<!-- Primary Metadata -->
<title>{pageTitle}</title>
<meta name="title" content={pageTitle} />
<meta name="description" content={pageDescription} />
<meta name="author" content={SITE.author} />

<!-- Color -->
<meta name="theme-color" content="" />
<meta name="color-scheme" content="light dark" />

<!-- Open Graph & Facebook -->
<meta property="og:type" content={pubDate ? 'article' : 'website'} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:title" content={pageTitle} />
<meta property="og:description" content={pageDescription} />
<meta property="og:image" content={ogImageURL} />
<meta property="og:site_name" content={siteName} />
<meta property="og:locale" content={SITE.ogLocale} />
{
  pubDate && (
    <>
      <meta property="article:author" content={SITE.author} />
      <meta property="article:published_time" content={pubDate} />
      <meta property="article:modified_time" content={lastModDate} />
    </>
  )
}

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={canonicalURL} />
<meta property="twitter:title" content={pageTitle} />
<meta property="twitter:description" content={pageDescription} />
<meta property="twitter:image" content={ogImageURL} />

<!-- Canonical URL -->
<link rel="canonical" href={canonicalURL} />

<!-- Icon -->
<link rel="icon" type="image/svg+xml" href={withBasePath('/favicon.svg')} />
<link rel="icon" sizes="32x32" href={withBasePath('/favicon.ico')} />
<link rel="apple-touch-icon" href={withBasePath('/apple-touch-icon.png')} />

<!-- Manifest -->
<link rel="manifest" href={withBasePath('/manifest.webmanifest')} />

<!-- Sitemap -->
<link rel="sitemap" href={withBasePath('/sitemap-index.xml')} />

<!-- RSS -->
<link
  rel="alternate"
  type="application/rss+xml"
  title={withBasePath('/rss.xml')}
  href="/rss.xml"
/>

<!-- Google JSON-LD Structured Data -->
{
  pubDate ? (
    <script
      is:inline
      type="application/ld+json"
      set:html={JSON.stringify(blogPostingData)}
    />
  ) : (
    <script
      is:inline
      type="application/ld+json"
      set:html={JSON.stringify(webPageData)}
    />
  )
}

<ClientRouter />
