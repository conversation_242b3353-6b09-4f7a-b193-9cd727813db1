---
import Link from '~/components/base/Link.astro'
import { resolvePath } from '~/utils/path'

import type { InternalNav, SocialLink } from '~/types'

type Props =
  | {
      type: 'internal'
      item: InternalNav
      mergeOnMobile: boolean
      mobileItemType?: 'text' | 'icon'
    }
  | {
      type: 'social'
      item: SocialLink
      mergeOnMobile: boolean
      mobileItemType?: 'text' | 'icon'
    }

const { type, item, mergeOnMobile, mobileItemType } = Astro.props

const href = type == 'internal' ? resolvePath(item.path) : item.link
const rel = type === 'internal' ? undefined : 'me'
const external = type === 'internal' ? false : true
const ariaCurrent =
  type === 'internal'
    ? Astro.url.pathname === resolvePath(item.path)
      ? 'page'
      : false
    : undefined
---

{
  mergeOnMobile ? (
    mobileItemType ? (
      mobileItemType === 'text' ? (
        (item.displayMode === 'alwaysText' ||
          item.displayMode === 'iconToTextOnMobile') && (
          <li class="block py-3 border-b b-[var(--c-scrollbar)] leading-6">
            <Link
              class="block op-transition"
              href={href}
              title={item.title}
              rel={rel}
              external={external}
              aria-current={ariaCurrent}
            >
              <span>{item.text}</span>
            </Link>
          </li>
        )
      ) : (
        (item.displayMode === 'alwaysIcon' ||
          item.displayMode === 'textToIconOnMobile') && (
          <Link
            class="block op-transition"
            href={href}
            title={item.title}
            rel={rel}
            external={external}
            aria-current={ariaCurrent}
          >
            <div class={item.icon} />
          </Link>
        )
      )
    ) : (
      <Link
        class="lt-md:hidden op-transition"
        href={href}
        title={item.title}
        rel={rel}
        external={external}
        aria-current={ariaCurrent}
      >
        {(item.displayMode === 'alwaysIcon' ||
          item.displayMode === 'iconHiddenOnMobile' ||
          item.displayMode === 'iconToTextOnMobile') && (
          <div class={item.icon} />
        )}
        {(item.displayMode === 'alwaysText' ||
          item.displayMode === 'textHiddenOnMobile' ||
          item.displayMode === 'textToIconOnMobile') && (
          <span>{item.text}</span>
        )}
      </Link>
    )
  ) : (
    <Link
      class={`${
        item.displayMode === 'iconHiddenOnMobile' ||
        item.displayMode === 'textHiddenOnMobile'
          ? 'lt-md:hidden'
          : ''
      } op-transition`}
      href={href}
      title={item.title}
      rel={rel}
      external={external}
      aria-current={ariaCurrent}
    >
      {(item.displayMode === 'alwaysText' ||
        item.displayMode === 'textHiddenOnMobile') && <span>{item.text}</span>}
      {(item.displayMode === 'alwaysIcon' ||
        item.displayMode === 'iconHiddenOnMobile') && <div class={item.icon} />}
      {item.displayMode === 'textToIconOnMobile' && (
        <>
          <span class="lt-md:hidden">{item.text}</span>
          <div class={`md:hidden ${item.icon}`} />
        </>
      )}
      {item.displayMode === 'iconToTextOnMobile' && (
        <>
          <span class="md:hidden">{item.text}</span>
          <div class={`lt-md:hidden ${item.icon}`} />
        </>
      )}
    </Link>
  )
}
