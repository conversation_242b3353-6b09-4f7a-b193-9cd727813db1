---

---

<button
  id="nav-open-button"
  class="hidden lt-md:block op-60 hover:op-100 op-transition"
  title="Navigation"
  aria-label="Navigation"
  aria-expanded="false"
  aria-controls="nav-panel"
>
  <div u-i-ri-menu-line></div>
</button>

<div
  id="nav-panel"
  class="z-200 fixed top-20 left-5% right-5%
    overflow-y-auto hidden! flex flex-col gap-5
    w-90% max-h-82vh py-6 px-8 rounded-lg
    bg-[var(--c-bg)] shadow-custom_0_0_20_0"
>
  <ul>
    <slot name="text" />
  </ul>
  <div class="flex justify-center items-center gap-4">
    <slot name="icon" />
  </div>
</div>

<script>
  import { toggleFadeEffect } from '~/utils/animation'

  document.addEventListener('astro:page-load', () => {
    /* Open nav panel when user click on button */
    const handleBtnClickToOpen = () => {
      toggleFadeEffect('backdrop', true, 'hidden')
      toggleFadeEffect('nav-panel', true, 'hidden!')
    }

    const navOpenButton = document.getElementById('nav-open-button')
    navOpenButton?.addEventListener('click', handleBtnClickToOpen)
  })
</script>
