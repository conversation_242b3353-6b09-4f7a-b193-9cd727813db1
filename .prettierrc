{"experimentalTernaries": false, "experimentalOperatorPosition": "end", "printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "consistent", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "objectWrap": "preserve", "bracketSameLine": false, "arrowParens": "always", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "plugins": ["prettier-plugin-astro"], "overrides": [{"files": "*.astro", "options": {"parser": "astro", "astroAllowShorthand": true, "astroSkipFrontmatter": false}}]}